import json
from main import receive_dingtalk_message
from flask import Flask, request

app = Flask(__name__)

# 模拟钉钉消息的测试数据
test_cases = {
    "分析命令": {
        "text": {
            "content": "分析 ************************************************************************************************************************************************************************************************************************************************************************************"
        },
        "senderStaffId": "test_user_001"
    },
    "磁盘告警": {
        "text": {
            "content": "node-disk-free-lower\n实例：192.168.1.1\n挂载点：/data"
        },
        "senderStaffId": "test_user_002"
    },
    "Kong服务告警": {
        "text": {
            "content": "告警规则: kong-service\n集群: test-cluster\nroute: test-route\n触发时间: 2024-03-20 10:00:00"
        },
        "senderStaffId": "test_user_003"
    },
    "Nginx告警": {
        "text": {
            "content": "实例：192.168.1.1\n域名：example.com\nport：80\nupstream：backend\n触发时间：2024-03-20 10:00:00"
        },
        "senderStaffId": "test_user_004"
    }
}

def test_dingtalk_message():
    """测试钉钉消息处理"""
    for case_name, test_data in test_cases.items():
        print(f"\n测试用例: {case_name}")
        print("输入数据:", json.dumps(test_data, ensure_ascii=False, indent=2))
        
        # 模拟请求
        with app.test_request_context(json=test_data):
            response = receive_dingtalk_message()
            print("响应数据:", json.dumps(response, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test_dingtalk_message() 